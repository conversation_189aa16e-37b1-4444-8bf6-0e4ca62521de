import numpy as np
import csv
import time
from collections import Counter, deque
import os
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping
import tensorflow.keras.backend as K
from tensorflow.keras.losses import binary_crossentropy

# --- 全局设置 (与之前相同) ---
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3
NUM_SIMULATION_ATTEMPTS = 100000


# --- 所有函数定义 (与之前版本相同) ---

# 1. 数据加载
def load_all_draws(filename="lottery_results_formatted.csv"):
    # ... 此函数未改变 ...
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。请确保CSV文件在同一目录下。")
        return []
    return all_draws


# 2. 独立号码特征工程
def calculate_individual_features(history_draws, red_range, blue_range):
    # ... 此函数未改变 ...
    red_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0,
        'positional_frequency': [0] * 5,
    } for num in red_range}
    blue_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0
    } for num in blue_range}
    if not history_draws: return red_features, blue_features
    last_seen_red = {num: -1 for num in red_range}
    last_seen_blue = {num: -1 for num in blue_range}
    for i, draw in enumerate(history_draws):
        for num in draw['red']: last_seen_red[num] = i
        for num in draw['blue']: last_seen_blue[num] = i
    total_draws = len(history_draws)
    for num in red_range: red_features[num]['omission'] = total_draws - 1 - last_seen_red[num]
    for num in blue_range: blue_features[num]['omission'] = total_draws - 1 - last_seen_blue[num]
    windowed_history = history_draws[-FREQUENCY_WINDOW:]
    red_counts, blue_counts = Counter(), Counter()
    positional_counts = {num: [0] * 5 for num in red_range}
    alpha = 0.1
    for draw in windowed_history:
        sorted_red = sorted(draw['red'])
        for idx, num in enumerate(sorted_red): positional_counts[num][idx] += 1
        for num in red_range:
            is_present = 1 if num in draw['red'] else 0
            red_counts[num] += is_present
            red_features[num]['heat'] = alpha * is_present + (1 - alpha) * red_features[num]['heat']
        for num in blue_range:
            is_present = 1 if num in draw['blue'] else 0
            blue_counts[num] += is_present
            blue_features[num]['heat'] = alpha * is_present + (1 - alpha) * blue_features[num]['heat']
    for num in red_range:
        red_features[num]['frequency'] = red_counts[num]
        red_features[num]['positional_frequency'] = positional_counts[num]
    for num in blue_range: blue_features[num]['frequency'] = blue_counts[num]
    return red_features, blue_features


# 3. 融合宏观特征的数据集创建
def create_composite_dataset(all_draws, red_range, blue_range):
    # ... 此函数未改变 ...
    X_red, Y_red, X_blue, Y_blue = [], [], [], []
    for i in range(FREQUENCY_WINDOW, len(all_draws)):
        history_for_features = all_draws[:i]
        target_draw, prev_draw = all_draws[i], all_draws[i - 1]
        red_features, blue_features = calculate_individual_features(history_for_features, red_range, blue_range)
        red_feature_vector_indiv = []
        for num in red_range:
            f = red_features[num]
            red_feature_vector_indiv.extend([f['omission'], f['frequency'], f['heat']])
            red_feature_vector_indiv.extend(f['positional_frequency'])
        prev_red = sorted(prev_draw['red'])
        prev_red_sum = sum(prev_red)
        prev_red_odd_count = sum(1 for n in prev_red if n % 2 != 0)
        tens_dist = [0] * 4
        for n in prev_red:
            if n < 10:
                tens_dist[0] += 1
            elif n < 20:
                tens_dist[1] += 1
            elif n < 30:
                tens_dist[2] += 1
            else:
                tens_dist[3] += 1
        intervals = [prev_red[j + 1] - prev_red[j] for j in range(len(prev_red) - 1)]
        global_features = [
                              prev_red_sum / 175, prev_red_odd_count / 5,
                              np.mean(intervals) / 34, np.std(intervals) / 17
                          ] + [c / 5 for c in tens_dist]
        final_red_feature_vector = red_feature_vector_indiv + global_features
        X_red.append(final_red_feature_vector)
        Y_red.append([1 if num in target_draw['red'] else 0 for num in red_range])
        blue_feature_vector = []
        for num in blue_range:
            f = blue_features[num]
            blue_feature_vector.extend([f['omission'], f['frequency'], f['heat']])
        X_blue.append(blue_feature_vector)
        Y_blue.append([1 if num in target_draw['blue'] else 0 for num in blue_range])
    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)


# 4. 定制化损失函数
def create_sum_regularized_loss(lambda_reg=0.1, target_sum=5.0):
    # 此内部函数的名字 'loss_fn' 在加载模型时需要用到
    def loss_fn(y_true, y_pred):
        bce = binary_crossentropy(y_true, y_pred)
        pred_sum = K.sum(y_pred, axis=-1)
        sum_error = K.square(pred_sum - target_sum)
        return bce + lambda_reg * sum_error

    return loss_fn


# 5. 模型结构与中奖检查
def create_dense_model(input_shape, output_shape, loss_function):
    # ... 此函数未改变 ...
    model = Sequential([
        Dense(256, activation='relu', input_shape=(input_shape,)),
        Dropout(0.4),
        Dense(128, activation='relu'),
        Dropout(0.4),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(output_shape, activation='sigmoid')
    ])
    model.compile(optimizer=Adam(learning_rate=0.0005), loss=loss_function, metrics=['accuracy'])
    return model


def check_prize(predicted_red, predicted_blue, actual_red, actual_blue):
    # ... 此函数未改变 ...
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hits = len(set(predicted_blue) & set(actual_blue))
    if red_hits == 5 and blue_hits == 2: return 1
    if red_hits == 5 and blue_hits == 1: return 2
    return 0


# --- 6. 主程序 ---
if __name__ == "__main__":
    start_time = time.time()
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)
    # --- 【新】定义模型保存路径 ---
    RED_MODEL_PATH = "red_ball_predictor.keras"
    BLUE_MODEL_PATH = "blue_ball_predictor.keras"

    print("--- 🎲 开始执行带模型持久化功能的彩票预测程序 ---")
    all_draws = load_all_draws()

    if len(all_draws) < FREQUENCY_WINDOW + NUM_TEST_PERIODS:
        print(f"数据不足，需要至少 {FREQUENCY_WINDOW + NUM_TEST_PERIODS} 期历史数据。")
    else:
        # 数据准备和分割环节提前，无论加载还是训练都需要测试集
        print("正在创建数据集...")
        X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_composite_dataset(
            all_draws, RED_BALL_RANGE, BLUE_BALL_RANGE
        )
        print("✅ 数据集创建完成。")
        X_red_train, Y_red_train = X_red_all[:-NUM_TEST_PERIODS], Y_red_all[:-NUM_TEST_PERIODS]
        X_blue_train, Y_blue_train = X_blue_all[:-NUM_TEST_PERIODS], Y_blue_all[:-NUM_TEST_PERIODS]
        X_red_test, Y_red_test = X_red_all[-NUM_TEST_PERIODS:], Y_red_all[-NUM_TEST_PERIODS:]
        X_blue_test, Y_blue_test = X_blue_all[-NUM_TEST_PERIODS:], Y_blue_all[-NUM_TEST_PERIODS:]

        # 准备自定义损失函数，加载和训练时都需要它
        red_loss = create_sum_regularized_loss(lambda_reg=0.1, target_sum=5.0)
        blue_loss = create_sum_regularized_loss(lambda_reg=0.05, target_sum=2.0)

        # --- 【新】模型加载或训练的核心逻辑 ---
        if os.path.exists(RED_MODEL_PATH) and os.path.exists(BLUE_MODEL_PATH):
            print(f"\n--- 🧠 发现本地模型 '{RED_MODEL_PATH}' 和 '{BLUE_MODEL_PATH}'，正在加载... ---")
            red_model = load_model(
                RED_MODEL_PATH,
                custom_objects={'loss_fn': red_loss}
            )
            blue_model = load_model(
                BLUE_MODEL_PATH,
                custom_objects={'loss_fn': blue_loss}
            )
            print("✅ 模型加载成功。跳过训练步骤。")
        else:
            print("\n--- 🧠 未发现本地模型，开始进入训练程序... ---")
            red_model = create_dense_model(X_red_train.shape[1], Y_red_train.shape[1], red_loss)
            blue_model = create_dense_model(X_blue_train.shape[1], Y_blue_train.shape[1], blue_loss)

            early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

            print("\n--- 🔴 开始训练红球模型 ---")
            red_model.fit(X_red_train, Y_red_train, epochs=100, batch_size=64, verbose=1, validation_split=0.2,
                          callbacks=[early_stopping])

            print("\n--- 🔵 开始训练蓝球模型 ---")
            blue_model.fit(X_blue_train, Y_blue_train, epochs=100, batch_size=64, verbose=1, validation_split=0.2,
                           callbacks=[early_stopping])

            print("\n--- 💾 训练完成，正在保存模型到本地... ---")
            red_model.save(RED_MODEL_PATH)
            blue_model.save(BLUE_MODEL_PATH)
            print(f"✅ 模型已成功保存。")

        # --- 回测与评估 (此部分代码无需改变) ---
        print(f"\n--- 🎯 在最近 {NUM_TEST_PERIODS} 期数据上执行回测 ---")
        # ... 后续的回测代码完全不变 ...
        for i in range(NUM_TEST_PERIODS):
            actual_red = {RED_BALL_RANGE.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
            actual_blue = {BLUE_BALL_RANGE.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

            print("\n" + "=" * 60)
            print(f"回测第 {i + 1} 期 | 实际号码: 红{sorted(list(actual_red))} 蓝{sorted(list(actual_blue))}")
            print("-" * 60)

            red_probs = red_model.predict(X_red_test[i:i + 1], verbose=0)[0]
            blue_probs = blue_model.predict(X_blue_test[i:i + 1], verbose=0)[0]

            top_10_red_indices = np.argsort(red_probs)[-10:]
            top_10_red = sorted([RED_BALL_RANGE.start + idx for idx in top_10_red_indices])
            top_10_prob_mass = np.sum(red_probs[top_10_red_indices])
            top_4_blue_indices = np.argsort(blue_probs)[-4:]
            top_4_blue = sorted([BLUE_BALL_RANGE.start + idx for idx in top_4_blue_indices])
            top_4_prob_mass = np.sum(blue_probs[top_4_blue_indices])

            print(f"⭐ 模型推荐高概率号码池:")
            print(f"   红球 (Top 10): {top_10_red}")
            print(f"   📈 模型信心: Top 10 红球占据了总概率的 {top_10_prob_mass:.2%}")
            print(f"   蓝球 (Top 4):  {top_4_blue}")
            print(f"   📈 模型信心: Top 4 蓝球占据了总概率的 {top_4_prob_mass:.2%}")
            print("-" * 60)

            print(f"执行 {NUM_SIMULATION_ATTEMPTS:,} 次模拟投注来评估模型效果...")
            red_probs_normalized = red_probs / np.sum(red_probs)
            blue_probs_normalized = blue_probs / np.sum(blue_probs)
            prize_counts = Counter()

            for attempt in range(NUM_SIMULATION_ATTEMPTS):
                pred_red_indices = np.random.choice(len(red_probs), size=5, replace=False, p=red_probs_normalized)
                pred_red = {RED_BALL_RANGE.start + idx for idx in pred_red_indices}
                pred_blue_indices = np.random.choice(len(blue_probs), size=2, replace=False, p=blue_probs_normalized)
                pred_blue = {BLUE_BALL_RANGE.start + idx for idx in pred_blue_indices}

                prize = check_prize(pred_red, pred_blue, actual_red, actual_blue)
                if prize > 0: prize_counts[prize] += 1
                if (attempt + 1) % 200000 == 0:
                    print(f"\r   ...已模拟 {attempt + 1:,} / {NUM_SIMULATION_ATTEMPTS:,} 次", end="")

            print("\n" + "-" * 60)
            if sum(prize_counts.values()) > 0:
                print(f"✅ 模拟完成! 在 {NUM_SIMULATION_ATTEMPTS:,} 次尝试中，命中结果如下:")
                for p_level, count in sorted(prize_counts.items()): print(f"   - {p_level}等奖: {count} 次")
            else:
                print(f"❌ 模拟完成! 在 {NUM_SIMULATION_ATTEMPTS:,} 次尝试中，未命中目标奖项。")

    print(f"\n\n程序总耗时: {(time.time() - start_time):.2f} 秒。")