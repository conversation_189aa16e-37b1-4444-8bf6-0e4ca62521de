import csv
import numpy as np
import random
import time
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Embedding
from tensorflow.keras.utils import to_categorical
from collections import Counter

# --- 模型权重设置 (可调整) ---
W_LSTM_RED = 0.4  # 红球LSTM权重
W_MARKOV_RED = 0.6  # 红球马尔科夫权重
W_LSTM_BLUE = 0.6  # 蓝球LSTM权重
W_MARKOV_BLUE = 0.4  # 蓝球马尔科夫权重


# --- 数据加载和准备 ---

def load_all_draws(filename="lottery_results_formatted.csv"):
    """加载所有历史开奖记录，并按日期降序返回（最新在前）。"""
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except Exception as e:
        print(f"文件加载失败: {e}")
        return []
    return all_draws


def prepare_data_for_lstm(sequence, seq_len=10, num_classes=36):
    X, y = [], []
    for i in range(len(sequence) - seq_len):
        X.append(sequence[i:i + seq_len])
        y.append(sequence[i + seq_len])
    return np.array(X), to_categorical(np.array(y), num_classes=num_classes)


# --- 模型创建 ---

def create_lstm_model(seq_len, vocab_size, lstm_units=50):
    model = Sequential([
        Embedding(input_dim=vocab_size, output_dim=10, input_length=seq_len),
        LSTM(lstm_units, return_sequences=True),  # 添加更多层
        LSTM(lstm_units),
        Dense(lstm_units, activation='relu'),
        Dense(vocab_size, activation='softmax')
    ])
    model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
    return model


def build_markov_matrix(sequence, num_states):
    matrix = np.zeros((num_states, num_states))
    for i in range(len(sequence) - 1):
        current, next_state = sequence[i], sequence[i + 1]
        if current < num_states and next_state < num_states:
            matrix[current][next_state] += 1
    for row in range(num_states):
        row_sum = matrix[row].sum()
        if row_sum > 0:
            matrix[row] /= row_sum
    return matrix


# --- 预测与评估 ---

def predict_with_ensemble(lstm_model, markov_matrix, history, num_predictions, seq_len, valid_range, vocab_size, w_lstm, w_markov):
    """增加权重参数用于个性化配置"""
    prediction = []
    current_sequence = list(history[-seq_len:])

    while len(prediction) < num_predictions:
        lstm_input = np.array([current_sequence])
        lstm_probs = lstm_model.predict(lstm_input, verbose=0)[0]

        markov_current_state = current_sequence[-1]
        markov_probs = np.zeros(vocab_size)
        if markov_current_state < len(markov_matrix):
            markov_probs = markov_matrix[markov_current_state]

        # 使用传入的权重参数
        combined_probs = (w_lstm * lstm_probs) + (w_markov * markov_probs)

        # 仅在有效范围内选择
        valid_probs = np.zeros_like(combined_probs)
        valid_indices = list(valid_range)
        valid_probs[valid_indices] = combined_probs[valid_indices]

        # 如果所有有效概率为0，则退回到均匀随机选择
        if valid_probs.sum() == 0:
            next_num = random.choice(valid_indices)
        else:
            valid_probs /= valid_probs.sum()
            next_num = np.random.choice(len(valid_probs), p=valid_probs)

        if next_num not in prediction:
            prediction.append(next_num)

        current_sequence.pop(0)
        current_sequence.append(next_num)

    prediction.sort()
    return prediction


def check_prize(predicted_red, predicted_blue, actual_red, actual_blue):
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hits = len(set(predicted_blue) & set(actual_blue))
    prize = 0
    if red_hits == 5 and blue_hits == 2:
        prize = 1
    elif red_hits == 5 and blue_hits == 1:
        prize = 2
    elif red_hits == 5 and blue_hits == 0:
        prize = 3
    elif red_hits == 4 and blue_hits == 2:
        prize = 4
    elif red_hits == 4 and blue_hits == 1:
        prize = 5
    elif red_hits == 3 and blue_hits == 2:
        prize = 6
    elif red_hits == 4 and blue_hits == 0:
        prize = 7
    elif (red_hits == 3 and blue_hits == 1) or (red_hits == 2 and blue_hits == 2):
        prize = 8
    elif (red_hits + blue_hits) >= 3:
        prize = 9  # 简化末等奖规则
    return red_hits, blue_hits, prize


# --- 主程序 ---
if __name__ == "__main__":
    SEQ_LENGTH = 10
    NUM_BACKTEST = 20
    start_time = time.time()

    print("--- 开始执行集成模型回测 ---")
    all_draws = load_all_draws()

    if not all_draws or len(all_draws) <= NUM_BACKTEST:
        print("历史数据不足以进行回测。")
    else:
        # 1. 数据分割
        test_draws = all_draws[:NUM_BACKTEST]
        train_draws = all_draws[NUM_BACKTEST:]
        print(f"数据分割 -> 训练集: {len(train_draws)}期, 测试集: {len(test_draws)}期")

        # 2. 在训练集上训练所有模型
        print("\n--- 正在构建和训练模型 (此过程可能需要较长时间) ---")
        train_red_seq = [num for draw in reversed(train_draws) for num in draw['red']]
        train_blue_seq = [num for draw in reversed(train_draws) for num in draw['blue']]

        red_markov_matrix = build_markov_matrix(train_red_seq, 36)
        blue_markov_matrix = build_markov_matrix(train_blue_seq, 13)
        print("马尔科夫矩阵构建完成。")

        X_red, y_red = prepare_data_for_lstm(train_red_seq, SEQ_LENGTH, 36)
        red_lstm_model = create_lstm_model(SEQ_LENGTH, 36)
        red_lstm_model.fit(X_red, y_red, epochs=10, batch_size=128, verbose=0)
        print("红球LSTM模型训练完成。")

        X_blue, y_blue = prepare_data_for_lstm(train_blue_seq, SEQ_LENGTH, 13)
        blue_lstm_model = create_lstm_model(SEQ_LENGTH, 13)
        blue_lstm_model.fit(X_blue, y_blue, epochs=10, batch_size=128, verbose=0)
        print("蓝球LSTM模型训练完成。")

        # 3. 开始回测循环
        print(f"\n--- 在 {len(test_draws)} 期测试数据上执行回测 ---")
        backtest_results = []
        full_red_history = [num for draw in reversed(all_draws) for num in draw['red']]
        full_blue_history = [num for draw in reversed(all_draws) for num in draw['blue']]

        for i, actual_draw in enumerate(test_draws):
            # 定位当前测试期在完整历史中的时间点
            history_cutoff = len(train_draws) + i
            red_hist_for_pred = full_red_history[:-history_cutoff * 5][-100:]  # 使用切片获取预测前的数据
            blue_hist_for_pred = full_blue_history[:-history_cutoff * 2][-100:]

            if len(red_hist_for_pred) < SEQ_LENGTH or len(blue_hist_for_pred) < SEQ_LENGTH:
                continue

            # 存储多组预测结果
            pred_red_list = []
            pred_blue_list = []
            
            # 生成10组预测
            for _ in range(10):
                pred_red = predict_with_ensemble(red_lstm_model, red_markov_matrix, red_hist_for_pred, 5, SEQ_LENGTH,
                                             range(1, 36), 36, W_LSTM_RED, W_MARKOV_RED)
                pred_blue = predict_with_ensemble(blue_lstm_model, blue_markov_matrix, blue_hist_for_pred, 2, SEQ_LENGTH,
                                             range(1, 13), 13, W_LSTM_BLUE, W_MARKOV_BLUE)
                pred_red_list.append(sorted(pred_red.copy()))
                pred_blue_list.append(sorted(pred_blue.copy()))

            # 为每组预测生成结果并统计
            for j in range(10):
                # 确保预测和实际结果正确存储
                rh, bh, pz = check_prize(pred_red_list[j], pred_blue_list[j], actual_draw['red'], actual_draw['blue'])
                backtest_results.append({
                    'pred_red': pred_red_list[j],
                    'pred_blue': pred_blue_list[j],
                    'actual_red': sorted(actual_draw['red'].copy()),
                    'actual_blue': sorted(actual_draw['blue'].copy()),
                    'red_hits': rh,
                    'blue_hits': bh,
                    'prize': pz
                })
                print(f"回测 {i + 1}-{j + 1}/10: 命中 {rh}+{bh}")

    # 5. 统计报告
    total_tests = len(backtest_results)
    avg_red = sum(r['red_hits'] for r in backtest_results) / total_tests
    avg_blue = sum(r['blue_hits'] for r in backtest_results) / total_tests
    wins = [r for r in backtest_results if r['prize'] > 0]

    print("\n" + "-" * 50)
    print("各期回测详情（预测 vs 实际）")
    print("-" * 50)
    for i, result in enumerate(backtest_results):
        print(f"第{i+1}期：")
        print(f"预测红球: {result['pred_red']} | 实际红球: {result['actual_red']}")
        print(f"预测蓝球: {result['pred_blue']} | 实际蓝球: {result['actual_blue']}")
        print(f"命中数: {result['red_hits']}+{result['blue_hits']} | 奖项等级: {result['prize']}")
        print("-" * 50)

    print("\n" + "=" * 50)
    print("        回测统计报告")
    print("=" * 50)
    print(f"总测试期数: {total_tests}")
    print(f"中奖期数: {len(wins)} (中奖率 {len(wins)/total_tests*100:.2f}%)")
    print(f"平均命中红球: {avg_red:.4f}个")
    print(f"平均命中蓝球: {avg_blue:.4f}个")
    print("=" * 50)

    if wins:
        prize_dist = Counter(r['prize'] for r in wins)
        print("\n各奖级命中次数分布:")
        for level, count in sorted(prize_dist.items()):
            print(f"  - {level}等奖: {count} 次")
        print("=" * 50)

    end_time = time.time()
    print(f"\n程序总耗时: {(end_time - start_time) / 60:.2f} 分钟。")