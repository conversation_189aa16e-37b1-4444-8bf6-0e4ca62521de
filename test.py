import numpy as np
import csv
import time
from collections import Counter, deque
import os
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping

# --- 全局设置 ---
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3


# --- 1. 数据加载 ---
def load_all_draws(filename="lottery_results_formatted.csv"):
    """加载所有历史开奖记录，并按时间正序（旧->新）返回。"""
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。")
        return []
    return all_draws


# --- 2. 特征工程核心 ---
def calculate_features(history_draws, red_range, blue_range):
    """根据历史开奖数据，为每个号码计算特征。"""
    red_features = {num: {'omission': 0, 'frequency': 0} for num in red_range}
    blue_features = {num: {'omission': 0, 'frequency': 0} for num in blue_range}

    if not history_draws:
        return red_features, blue_features

    # 使用 deque 来高效地管理滑动窗口
    red_window_sets = deque(maxlen=FREQUENCY_WINDOW)
    blue_window_sets = deque(maxlen=FREQUENCY_WINDOW)

    # 为了正确计算遗漏值，我们需要从头遍历
    # 初始化所有号码的遗漏值
    for num in red_range: red_features[num]['omission'] = len(history_draws)
    for num in blue_range: blue_features[num]['omission'] = len(history_draws)

    last_seen_red = {num: -1 for num in red_range}
    last_seen_blue = {num: -1 for num in blue_range}

    for i, draw in enumerate(history_draws):
        red_window_sets.append(set(draw['red']))
        blue_window_sets.append(set(draw['blue']))
        for num in draw['red']:
            last_seen_red[num] = i
        for num in draw['blue']:
            last_seen_blue[num] = i

    # 计算最终遗漏值
    for num in red_range:
        red_features[num]['omission'] = (len(history_draws) - 1) - last_seen_red[num]
    for num in blue_range:
        blue_features[num]['omission'] = (len(history_draws) - 1) - last_seen_blue[num]

    # 计算窗口内频率
    red_counts = Counter(num for draw_set in red_window_sets for num in draw_set)
    for num in red_range:
        red_features[num]['frequency'] = red_counts.get(num, 0)

    blue_counts = Counter(num for draw_set in blue_window_sets for num in draw_set)
    for num in blue_range:
        blue_features[num]['frequency'] = blue_counts.get(num, 0)

    return red_features, blue_features


# --- 3. 创建基于特征的数据集 ---
def create_feature_dataset(all_draws, red_range, blue_range):
    """将整个开奖历史转换为 (特征, 标签) 的数据集"""
    X_red, Y_red, X_blue, Y_blue = [], [], [], []
    for i in range(FREQUENCY_WINDOW, len(all_draws)):
        history = all_draws[:i]
        target_draw = all_draws[i]
        red_features, blue_features = calculate_features(history, red_range, blue_range)

        red_feature_vector = [val for num in red_range for val in red_features[num].values()]
        blue_feature_vector = [val for num in blue_range for val in blue_features[num].values()]

        red_label_vector = [1 if num in target_draw['red'] else 0 for num in red_range]
        blue_label_vector = [1 if num in target_draw['blue'] else 0 for num in blue_range]

        X_red.append(red_feature_vector)
        Y_red.append(red_label_vector)
        X_blue.append(blue_feature_vector)
        Y_blue.append(blue_label_vector)

    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)


# --- 4. 新的模型结构 ---
def create_dense_model(input_shape, output_shape, loss_function):
    """创建一个全连接神经网络模型"""
    model = Sequential([
        Dense(128, activation='relu', input_shape=(input_shape,)),
        Dropout(0.3),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(output_shape, activation='sigmoid')
    ])
    model.compile(optimizer=Adam(learning_rate=0.001), loss=loss_function, metrics=['accuracy'])
    return model


def check_prize(predicted_red, predicted_blue, actual_red, actual_blue):
    """核对中奖情况"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hits = len(set(predicted_blue) & set(actual_blue))
    prize = 0
    if red_hits == 5 and blue_hits == 2:
        prize = 1
    elif red_hits == 5 and blue_hits == 1:
        prize = 2
    return red_hits, blue_hits, prize


# --- 5. 主程序 ---
if __name__ == "__main__":
    start_time = time.time()
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)

    # --- 【新】定义此版本模型的保存路径 ---
    RED_MODEL_PATH = "basic_red_model.keras"
    BLUE_MODEL_PATH = "basic_blue_model.keras"

    print("--- 🎲 开始执行【基础版】彩票预测程序 ---")
    all_draws = load_all_draws()

    if len(all_draws) < FREQUENCY_WINDOW + NUM_TEST_PERIODS:
        print("数据不足。")
    else:
        # 数据准备和分割
        print(f"正在从 {len(all_draws)} 期历史数据中创建特征数据集...")
        X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_feature_dataset(all_draws, RED_BALL_RANGE,
                                                                              BLUE_BALL_RANGE)
        print("✅ 特征数据集创建完成。")

        X_red_train = X_red_all[:-NUM_TEST_PERIODS]
        Y_red_train = Y_red_all[:-NUM_TEST_PERIODS]
        X_blue_train = X_blue_all[:-NUM_TEST_PERIODS]
        Y_blue_train = Y_blue_all[:-NUM_TEST_PERIODS]

        X_red_test = X_red_all[-NUM_TEST_PERIODS:]
        Y_red_test = Y_red_all[-NUM_TEST_PERIODS:]
        X_blue_test = X_blue_all[-NUM_TEST_PERIODS:]
        Y_blue_test = Y_blue_all[-NUM_TEST_PERIODS:]

        # --- 【新】模型加载或训练的核心逻辑 ---
        if os.path.exists(RED_MODEL_PATH) and os.path.exists(BLUE_MODEL_PATH):
            print(f"\n--- 🧠 发现本地模型 '{RED_MODEL_PATH}' 和 '{BLUE_MODEL_PATH}'，正在加载... ---")
            red_model = load_model(RED_MODEL_PATH)
            blue_model = load_model(BLUE_MODEL_PATH)
            print("✅ 模型加载成功，跳过训练。")
        else:
            print(f"\n数据分割 -> 训练/验证集: {len(X_red_train)}期, 最终回测集: {len(X_red_test)}期")
            print("\n--- 🧠 未发现本地模型，开始训练... ---")
            red_model = create_dense_model(X_red_train.shape[1], Y_red_train.shape[1], 'binary_crossentropy')
            blue_model = create_dense_model(X_blue_train.shape[1], Y_blue_train.shape[1], 'binary_crossentropy')

            early_stopping = EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True)

            print("\n--- 🔴 开始训练红球模型 (80%训练, 20%验证) ---")
            red_model.fit(X_red_train, Y_red_train,
                          epochs=200, batch_size=128, verbose=1,
                          validation_split=0.2,
                          callbacks=[early_stopping])

            print("\n--- 🔵 开始训练蓝球模型 (80%训练, 20%验证) ---")
            blue_model.fit(X_blue_train, Y_blue_train,
                           epochs=200, batch_size=128, verbose=1,
                           validation_split=0.2,
                           callbacks=[early_stopping])

            print("\n--- 💾 训练完成，正在保存模型到本地... ---")
            red_model.save(RED_MODEL_PATH)
            blue_model.save(BLUE_MODEL_PATH)
            print(f"✅ 模型已成功保存。")

        # --- 回测部分（无需任何改动） ---
        print(f"\n--- 🎯 在最近 {NUM_TEST_PERIODS} 期数据上执行回测 (目标: 1等或2等奖) ---")
        for i in range(NUM_TEST_PERIODS):
            red_test_features = X_red_test[i:i + 1]
            blue_test_features = X_blue_test[i:i + 1]
            actual_red = {RED_BALL_RANGE.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
            actual_blue = {BLUE_BALL_RANGE.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

            print("\n" + "=" * 60)
            print(f"回测第 {i + 1} 期... | 实际号码: 红{sorted(list(actual_red))} 蓝{sorted(list(actual_blue))}")
            print("正在循环搜索一等或二等奖，请耐心等待...")
            print("-" * 60)

            attempts = 0
            is_hit = False

            red_probs = red_model.predict(red_test_features, verbose=0)[0]
            red_probs_normalized = red_probs / np.sum(red_probs)

            blue_probs = blue_model.predict(blue_test_features, verbose=0)[0]
            blue_probs_normalized = blue_probs / np.sum(blue_probs)

            while not is_hit:
                attempts += 1

                predicted_red_indices = np.random.choice(len(red_probs), size=5, replace=False, p=red_probs_normalized)
                predicted_red = {RED_BALL_RANGE.start + idx for idx in predicted_red_indices}

                predicted_blue_indices = np.random.choice(len(blue_probs), size=2, replace=False,
                                                          p=blue_probs_normalized)
                predicted_blue = {BLUE_BALL_RANGE.start + idx for idx in predicted_blue_indices}

                rh, bh, pz = check_prize(predicted_red, predicted_blue, actual_red, actual_blue)

                if pz in [1, 2]:
                    is_hit = True
                    print(f"\n🎉🎉🎉 命中目标! 奖项: {pz}等奖! 🎉🎉🎉")
                    print(f"  >>>【 本期尝试总次数: {attempts:,} 】<<<")
                    print(f"  - 预测号码: 红{sorted(list(predicted_red))} 蓝{sorted(list(predicted_blue))}")
                    print(f"  - 命中结果: {rh}红 + {bh}蓝")

                if attempts % 10000 == 0 and not is_hit:
                    print(f"\r      [进度] 预测号码 {attempts:,} 次...", end="")

    print(f"\n\n程序总耗时: {(time.time() - start_time):.2f} 秒。")