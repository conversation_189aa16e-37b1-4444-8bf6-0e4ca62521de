from bs4 import BeautifulSoup
import csv

def parse_lottery_data(html_content):
    """
    从提供的HTML内容中解析大乐透开奖数据，并按指定格式输出。

    Args:
        html_content (str): 网页的HTML内容。

    Returns:
        list: 包含开奖日期和号码的列表，如果失败则返回None。
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    data_list = []

    data_tbody = soup.find('tbody', id='data-tab')
    if not data_tbody:
        print("错误：在HTML中未找到ID为 'data-tab' 的<tbody>元素。")
        return None

    rows = data_tbody.find_all('tr')
    if not rows:
        print("未找到任何开奖数据行(<tr>)，请检查HTML文件内容。")
        return None

    for row in rows:
        try:
            cells = row.find_all('td')

            if len(cells) < 4:
                continue

            # 获取并格式化日期
            full_date_text = cells[1].text.strip()
            date = full_date_text.split(' ')[0]

            # 获取号码文本
            red_balls_text = cells[2].text.strip()
            blue_balls_text = cells[3].text.strip()

            red_balls = red_balls_text.split()
            blue_balls = blue_balls_text.split()

            # ---【修正 1】---
            # 使用 ' | ' 分隔红球和蓝球，保持视觉上的清晰分隔。
            numbers = '-'.join(red_balls) + ' | ' + '-'.join(blue_balls)

            if date and numbers:
                data_list.append([date, numbers])

        except IndexError:
            print(f"警告：跳过一个不完整的行。内容: {row.text.strip()}")
            continue
        except Exception as e:
            print(f"处理数据时出错: {e}")
            continue

    # 在返回数据前按日期排序
    data_list.sort(key=lambda x: x[0])  # 按日期升序排列
    return data_list


def save_to_csv(data, filename="lottery_results_formatted.csv"):
    """
    将数据保存到CSV文件。

    Args:
        data (list): 要保存的数据列表。
        filename (str): CSV文件名。
    """
    if not data:
        print("没有数据可保存。")
        return

    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            # ---【修正 2】---
            # 添加了CSV的表头，让文件内容更清晰。
            writer.writerow(['开奖日期', '开奖号码'])
            writer.writerows(data)
        print(f"\n数据已成功保存到 {filename}")
    except IOError as e:
        print(f"保存文件时出错: {e}")


# --- 主程序 ---
if __name__ == "__main__":
    html_file_path = "response.html"

    try:
        print(f"正在从本地文件 '{html_file_path}' 读取数据...")
        with open(html_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误：无法找到文件 '{html_file_path}'。请确保该文件与脚本在同一目录下。")
        exit()

    lottery_data = parse_lottery_data(content)

    if lottery_data:
        print("\n--- 解析并格式化后的部分数据 ---")
        for item in lottery_data[:10]:
            print(f"日期: {item[0]}, 号码: {item[1]}")

        save_to_csv(lottery_data)